import { LegalLinkTemplateId, RegisterQuestions, SignatureMention } from '@mynotary/crossplatform/legal-templates/api';
import { isPersonneMorale, isPersonnePhysique } from '@mynotary/crossplatform/legal-templates/api';
import { LegalRecord } from '@mynotary/crossplatform/records/api';
import { formatPriceToNumberAndLetter } from '@mynotary/crossplatform/shared/util';

export type GetDefaultSignatoriesArgs = {
  linkedRecords: LinkedRecord[];
};

export type DefaultSignatory = {
  config?: DefaultSignatoryConfig;
  record: LegalRecord;
};

export interface ContractSignatureConfig {
  getDefaultSignatories: (args: GetDefaultSignatoriesArgs) => DefaultSignatory[];
}

export type RecordWithLinkedRecord = LegalRecord & {
  branchType?: string;
  linkedRecords: LinkedRecord[];
};

export type LinkedRecord = {
  linkTemplateId: LegalLinkTemplateId;
  records: RecordWithLinkedRecord[];
};

type DefaultSignatoryConfig = {
  defaultGroup?: number;
  mention?: string;
};

export function getBailBailleurOrRepresentantSignatories({
  ctx
}: {
  ctx: GetDefaultSignatoriesArgs;
}): DefaultSignatory[] {
  const mandatRecord = getRecordsInOperation({
    branchType: 'MANDAT',
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__FICHES'
  })[0];

  return mandatRecord?.answer?.['mandat_type']?.value === 'gestion'
    ? createDefaultSignatoriesFromRecords({
        ctx,
        linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES'
      })
    : createDefaultSignatoriesFromRecords({
        ctx,
        linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__BAILLEURS'
      });
}

export function getBailBailleurOrSignataireAgenceSignatories({
  ctx
}: {
  ctx: GetDefaultSignatoriesArgs;
}): DefaultSignatory[] {
  const locationRecord = getRecordsInOperation({
    branchType: 'LOCATION',
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__FICHES'
  })[0];

  return locationRecord?.answer?.['bail_representation_agence']?.value === 'oui'
    ? createDefaultSignatoriesFromRecords({
        ctx,
        linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__SIGNATAIRE_AGENCE'
      })
    : createDefaultSignatoriesFromRecords({
        ctx,
        linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__BAILLEURS'
      });
}

export function getBailCommercialBailleurOrRepresentantSignatories({
  ctx
}: {
  ctx: GetDefaultSignatoriesArgs;
}): DefaultSignatory[] {
  const mandatRecord = getRecordsInOperation({
    branchType: 'MANDAT',
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL'
  })[0];

  return mandatRecord?.answer?.['mandat_gestion_statut']?.value === 'oui'
    ? createDefaultSignatoriesFromRecords({
        ctx,
        linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES'
      })
    : createDefaultSignatoriesFromRecords({
        ctx,
        linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__BAILLEURS'
      });
}

/**
 * retrieve the people who are signatories of the contract
 */
export function retrieveRepresentativeLegalRecords(contractLinkRecord: RecordWithLinkedRecord): LegalRecord[] {
  if (isPersonneMorale(contractLinkRecord.templateId)) {
    return retrieveMoralPersonRepresentative(contractLinkRecord);
  } else if (isPersonnePhysique(contractLinkRecord.templateId)) {
    return retrievePhysicalPersonRepresentative(contractLinkRecord);
  }

  return [];
}

function retrieveMoralPersonRepresentative(contractLinkRecord: RecordWithLinkedRecord) {
  const procurations = getLinkedRecord(contractLinkRecord, 'LINK__PROCURATION__PROCURATION');

  if (procurations.length > 0) {
    return procurations;
  }

  const representants = getLinkedRecord(contractLinkRecord, 'LINK__REPRESENTATION__PERSONNE_MORALE');

  if (representants.length > 0) {
    return representants;
  }

  return [];
}

function retrievePhysicalPersonRepresentative(recordWithLinkedRecord: RecordWithLinkedRecord): LegalRecord[] {
  let procurations = getLinkedRecord(recordWithLinkedRecord, 'LINK__PROCURATION__PROCURATION');
  // ici on récupérait aussi : "RECORD__EXTENSION__ANCIEN__VENDEUR"
  procurations = procurations.filter((record) => record.templateId === 'RECORD__PERSONNE__PHYSIQUE');

  if (procurations.length > 0) {
    return procurations;
  }

  const curatelle = getLinkedRecord(recordWithLinkedRecord, 'LINK__CAPACITE__CURATELLE');
  if (curatelle.length > 0) {
    return [recordWithLinkedRecord, ...curatelle];
  }

  const habilitation = getLinkedRecord(recordWithLinkedRecord, 'LINK__CAPACITE__HABILITATION_FAMILIALE');
  if (habilitation.length > 0) {
    return habilitation;
  }

  const mandatProtectionFuture = getLinkedRecord(recordWithLinkedRecord, 'LINK__CAPACITE__MANDAT_PROTECTION_FUTURE');
  if (mandatProtectionFuture.length > 0) {
    return mandatProtectionFuture;
  }

  const minorite = getLinkedRecord(recordWithLinkedRecord, 'LINK__CAPACITE__MINORITE');
  if (minorite.length > 0) {
    return minorite;
  }

  const tutelle = getLinkedRecord(recordWithLinkedRecord, 'LINK__CAPACITE__TUTELLE');
  if (tutelle.length > 0) {
    return tutelle;
  }

  return [recordWithLinkedRecord];
}

export function createDefaultSignatoriesFromRecords({
  config,
  ctx,
  linkTemplateId
}: {
  config?: DefaultSignatoryConfig;
  ctx: GetDefaultSignatoriesArgs;
  linkTemplateId: LegalLinkTemplateId;
}): DefaultSignatory[] {
  const records = getRecordsInOperation({ ctx, linkTemplateId });
  const recordsFlatten = records.flatMap(retrieveRepresentativeLegalRecords).map((record) => ({
    config,
    record
  }));

  const uniqueIds = new Set();
  const uniqueRecords = recordsFlatten.filter((item) => {
    if (!uniqueIds.has(item.record.id)) {
      uniqueIds.add(item.record.id);
      return true;
    }
    return false;
  });

  return uniqueRecords;
}

export function getRecordsInOperation({
  branchType,
  ctx,
  linkTemplateId
}: {
  branchType?: string;
  ctx: GetDefaultSignatoriesArgs;
  linkTemplateId: LegalLinkTemplateId;
}): RecordWithLinkedRecord[] {
  return (
    ctx.linkedRecords
      .find((record) => record.linkTemplateId === linkTemplateId)
      ?.records.filter((record) => (branchType != null ? record.branchType === branchType : true)) || []
  );
}

function getLinkedRecord(
  record: RecordWithLinkedRecord,
  legalLinkTemplateId: LegalLinkTemplateId
): RecordWithLinkedRecord[] {
  return record.linkedRecords.find((record) => record.linkTemplateId === legalLinkTemplateId)?.records || [];
}

export function getMention({ ctx, mentions }: { ctx: GetDefaultSignatoriesArgs; mentions: SignatureMention }): string {
  if (mentions == null || mentions.variables == null) {
    return mentions.text ?? '';
  }
  const variablesMap = Object.entries(mentions.variables).map(([key, mentionVar]) => {
    const [linkId, , branchType] = mentionVar.path;
    const linkTemplateId = `LINK__${linkId}` as LegalLinkTemplateId;

    const records = getRecordsInOperation({ branchType, ctx, linkTemplateId });

    const value = records.map((record) => computeRegisterQuestion(record, mentionVar)).join(' ');

    return { [key]: value };
  });

  let mentionText = mentions.text;
  for (const variable of variablesMap) {
    const [key, value] = Object.entries(variable)[0];
    mentionText = mentionText?.replace(`{{ ${key} }}`, value);
  }

  return mentionText;
}

function computeRegisterQuestion(legalRecord: LegalRecord, registerQuestion: RegisterQuestions) {
  if (registerQuestion.type == null) {
    return '';
  }
  return methods[registerQuestion.type](legalRecord, registerQuestion);
}

function addElements(legalRecord: LegalRecord, registerQuestion: RegisterQuestions): number {
  return registerQuestion.items.reduce(
    (acc, item) => acc + (item.type === 'QUESTION_ID' ? (legalRecord.answer?.[item.value]?.value ?? 0) : item.value),
    0
  );
}

function concatElements(legalRecord: LegalRecord, registerQuestion: RegisterQuestions): string {
  return registerQuestion.items
    .map((item) => {
      return `${item.withPrefix ?? ''}${item.type === 'QUESTION_ID' ? (legalRecord.answer?.[item.value]?.value ?? '') : item.value}`;
    })
    .join('');
}

type VariableType = 'ADD' | 'CONCAT';

type CallbackFunction = (legalRecord: LegalRecord, registerQuestion: RegisterQuestions) => string;

const methods: Record<VariableType, CallbackFunction> = {
  ADD: (legalRecord, registerQuestion) => formatPriceToNumberAndLetter(addElements(legalRecord, registerQuestion)),
  CONCAT: concatElements
};
