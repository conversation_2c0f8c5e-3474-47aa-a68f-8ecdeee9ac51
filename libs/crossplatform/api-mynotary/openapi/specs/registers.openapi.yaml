openapi: 3.0.0
info:
  title: registers
  version: '1.0'
paths:
  '/register-entries':
    get:
      tags: [ 'Registers Entries' ]
      summary: Get register entries list
      parameters:
        - name: organizationId
          schema:
            type: string
          in: query
        - name: legalOperationId
          schema:
            type: string
          in: query
        - name: userId
          schema:
            type: string
          in: query
          description: The ID of the user querying.
        - name: page
          schema:
            type: number
          in: query
          required: true
          description: The page number to retrieve.
        - name: pageSize
          schema:
            type: number
          in: query
          required: true
          description: The number of entries per page.
        - name: search
          schema:
            type: string
          in: query
        - name: entryNumber
          schema:
            type: number
          in: query
        - name: type
          schema:
            $ref: '#/components/schemas/RegisterType'
          in: query
          required: true
      responses:
        '200':
          description: Register entries list
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterEntryList'
  '/transaction-registers':
    get:
      tags: ['Registers']
      summary: Get transaction register configuration
      parameters:
        - schema:
            type: string
          name: organizationId
          in: query
          required: true
      responses:
        '200':
          description: Transaction register configuration
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TransactionRegisterConfig'
  '/transaction-registers/{id}':
    put:
      summary: Update transaction register configuration
      tags: ['Registers']
      responses:
        '204':
          description: No Content
      parameters:
        - schema:
            type: string
          name: id
          in: path
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TransactionRegisterUpdate'
  '/management-registers':
    get:
      tags: ['Registers']
      summary: Get management register configuration
      parameters:
        - schema:
            type: string
          name: organizationId
          in: query
          required: true
      responses:
        '200':
          description: Management register configuration
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementRegisterConfig'
  '/management-registers/{id}':
    put:
      summary: Update management register configuration
      tags: ['Registers']
      responses:
        '204':
          description: No Content
      parameters:
        - schema:
            type: string
          name: id
          in: path
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementRegisterUpdate'
  '/receivership-registers':
    get:
      tags: ['Registers']
      summary: Get receivership register configuration
      parameters:
        - schema:
            type: string
          name: organizationId
          in: query
          required: true
      responses:
        '200':
          description: Receivership register configuration
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReceivershipRegisterConfig'
  '/receivership-registers/{id}':
    put:
      summary: Update receivership register configuration
      tags: ['Registers']
      responses:
        '204':
          description: No Content
      parameters:
        - schema:
            type: string
          name: id
          in: path
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReceivershipRegisterUpdate'
components:
  schemas:
    ReceivershipRegisterUpdate:
      properties:
        organizationId:
          type: string
        config:
          type: object
          nullable: false
          properties:
            cardNumber:
              type: string
            initialValue:
              type: number
            receivershipBank:
              type: string
            receivershipBankAccount:
              type: string
            warrantyAmount:
              type: number
            warrantyCompany:
              type: string
          required:
            - cardNumber
            - initialValue
            - receivershipBank
            - receivershipBankAccount
            - warrantyAmount
            - warrantyCompany
      required:
        - organizationId
    ManagementRegisterUpdate:
      properties:
        organizationId:
          type: string
        config:
          type: object
          nullable: false
          properties:
            cardNumber:
              type: string
            initialValue:
              type: number
          required:
            - cardNumber
            - initialValue
      required:
        - organizationId
    TransactionRegisterUpdate:
      properties:
        config:
          type: object
          nullable: false
          properties:
            cardNumber:
              type: string
            initialValue:
              type: number
          required:
            - cardNumber
            - initialValue
        organizationId:
          type: string
      required:
        - organizationId
    TransactionRegisterConfig:
      properties:
        id:
          type: string
        organizationId:
          type: string
        config:
          type: object
          nullable: true
          properties:
            cardNumber:
              type: string
            initialValue:
              type: number
          required:
            - cardNumber
            - initialValue
      required:
        - id
        - organizationId

    ManagementRegisterConfig:
      properties:
        id:
          type: string
        organizationId:
          type: string
        config:
          type: object
          nullable: true
          properties:
            cardNumber:
              type: string
            initialValue:
              type: number
          required:
            - cardNumber
            - initialValue
      required:
        - id
        - organizationId

    ReceivershipRegisterConfig:
      properties:
        id:
          type: string
        organizationId:
          type: string
        config:
          type: object
          nullable: true
          properties:
            cardNumber:
              type: string
            initialValue:
              type: number
            receivershipBank:
              type: string
            receivershipBankAccount:
              type: string
            warrantyAmount:
              type: number
            warrantyCompany:
              type: string
          required:
            - cardNumber
            - initialValue
            - receivershipBank
            - receivershipBankAccount
            - warrantyAmount
            - warrantyCompany
      required:
        - id
        - organizationId
    RegisterEntryList:
      type: object
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/RegisterEntry'
        total:
          type: number
      required:
        - items
        - total
    RegisterEntry:
      type: object
      properties:
        answer:
          $ref: '../shared-components/answer.openapi.yaml#/AnswerDict'
        creationTime:
          type: string
        creatorEmail:
          type: string
        creatorFirstname:
          type: string
        creatorLastname:
          type: string
        creatorId:
          type: string
        legalOperationId:
          type: string
        id:
          type: string
        status:
          $ref: '#/components/schemas/RegisterStatus'
        type:
          $ref: '#/components/schemas/RegisterType'
      required:
        - answer
        - creationTime
        - creatorEmail
        - creatorFirstname
        - creatorLastname
        - creatorId
        - legalOperationId
        - id
        - status
        - type
    RegisterStatus:
      type: string
      enum:
        - CLOSED
        - RESERVED
        - VALIDATED
    RegisterType:
      type: string
      enum:
        - MANAGEMENT
        - RECEIVERSHIP
        - TRANSACTION
