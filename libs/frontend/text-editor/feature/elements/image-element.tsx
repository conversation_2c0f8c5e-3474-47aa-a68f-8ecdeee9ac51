import {
  Image,
  useMediaState,
  PlateElement,
  ResizableProvider,
  TImageElement
} from '@mynotary/frontend/text-editor/core';
import { classNames } from '@mynotary/frontend/shared/util';
import { MediaPopover } from '../toolbar/image/media-popover';
import { mediaResizeHandleVariants, Resizable, ResizeHandle } from './resizable';
import { PlateElementProps, withHOC } from '@udecode/plate/react';
import { useTextEditorReadOnly } from '../read-only-context';

/**
 * This element is based on plate. we don't use caption and customize MediaPopover and we have our own css
 * We've added useTextEditorReadOnly to disable the resize handle when image is used in a non editable parent
 */

export const ImageElement = withHOC(ResizableProvider, (props: PlateElementProps<TImageElement>) => {
  const { align = 'center', readOnly, selected } = useMediaState();
  const textEditorReadOnly = useTextEditorReadOnly();
  const attributes = { ...props.attributes, className: classNames('ie-image', selected && 'ie-image-selected') };

  if (textEditorReadOnly) {
    return (
      <PlateElement {...props} className='image-element'>
        <figure className='ie-figure' contentEditable={false}>
          <Image draggable={false} unselectable='on' {...attributes} />
        </figure>

        {props.children}
      </PlateElement>
    );
  }

  return (
    <MediaPopover>
      <PlateElement {...props} className='image-element'>
        <figure className='ie-figure' contentEditable={false}>
          <Resizable
            align={align}
            options={{
              align,
              readOnly
            }}
          >
            <ResizeHandle
              className={mediaResizeHandleVariants({ direction: 'left' })}
              options={{ direction: 'left' }}
            />
            <Image draggable={false} {...attributes} />
            <ResizeHandle
              className={mediaResizeHandleVariants({ direction: 'right' })}
              options={{ direction: 'right' }}
            />
          </Resizable>
        </figure>

        {props.children}
      </PlateElement>
    </MediaPopover>
  );
});
