import './register-question-common.scss';
import { useParams } from 'react-router-dom';
import React, { ReactElement, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { MnConfirmationPopin, MnInputText, MnSvg, MnTooltip } from '@mynotary/frontend/shared/ui';
import { selectOperation, selectContract } from '@mynotary/frontend/legals/api';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { RegisterAddEntryPopin } from '../add-register-entry-popin/add-register-entry-popin';
import { RegisterEntry } from '@mynotary/frontend/registers/core';
import { getRegisterEntries, selectContractRegisterDefaultAnswer } from '@mynotary/frontend/registers/store';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';

interface RegisterQuestionCommonProps extends MnProps {
  answer: Answer;
  canAddManually: boolean;
  canRead: boolean;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterQuestionCommon = ({
  answer,
  canAddManually,
  canRead,
  className,
  debounce,
  disabled,
  onChange,
  question
}: RegisterQuestionCommonProps): ReactElement => {
  const dispatch = useAsyncDispatch();
  const { contractId, id } = useParams<{ contractId: string; id: string }>();
  const organization = useSelector(selectCurrentOrganization);
  const operationId = parseInt(id ?? '');
  const operation = useSelector(selectOperation(operationId));
  const contract = useSelector(selectContract(parseInt(contractId ?? '')));
  const contractAnswer = useSelector(
    selectContractRegisterDefaultAnswer(question.register?.type, operation?.id, contract?.id)
  );

  const [entry, setEntry] = useState<RegisterEntry>();
  const [openConfirmationPopin, setOpenConfirmationPopin] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [isOnFocusInput, setIsOnFocusInput] = useState(false);

  const isClosed = !entry || entry.status === 'CLOSED';
  const isButtonEnabled = !disabled && isClosed;

  useEffect(() => {
    if (!canRead || answer?.value == null || organization == null) {
      return;
    }
    const getRegisterEntry = async () => {
      const register = await dispatch(
        getRegisterEntries(
          question.register.type,
          `"numero_registre": {"value": ${answer.value}}`,
          0,
          1,
          organization.id
        )
      );
      setEntry(register?.entries?.[0]);
    };

    getRegisterEntry();
  }, [answer?.value, canRead, dispatch, organization, question.register.type]);

  useEffect(() => {
    if (entry == null || !isOnFocusInput || entry?.operationId === operationId) {
      return;
    }
    setOpenConfirmationPopin(true);
  }, [entry, answer?.value, isOnFocusInput, operationId]);

  const handleRegisterFinished = (entry?: RegisterEntry): void => {
    setIsAdding(false);
    setEntry(entry);
    onChange?.(entry?.answer['numero_registre'].value);
  };

  const handleCancelPopin = (): void => {
    setOpenConfirmationPopin(false);
    setEntry(undefined);
    setIsOnFocusInput(false);
    onChange?.(null);
  };

  const handleError = async () => {
    dispatch(setErrorMessage('Une erreur est survenue lors de la prise de numéro. Vouz pouvez annuler et réessayer.'));
    setIsAdding(false);
  };

  const handleAddRegistry = (): void => {
    if (isButtonEnabled) {
      setIsAdding(true);
    }
  };

  const getTooltipContent = (disabled: boolean, isClosed: boolean): string => {
    if (disabled) {
      return 'Vous ne pouvez pas prendre de numéro sur un contrat validé';
    } else if (!isClosed) {
      return 'Vous ne pouvez pas reprendre un numéro sans clore le précédent';
    }
    return '';
  };

  return (
    <div className={classNames('mn-register-question')}>
      <MnInputText
        className={className}
        debounceTime={debounce ? 500 : 0}
        defaultValue={question.default}
        disabled={disabled || !canAddManually || !isClosed}
        format={question.uppercase}
        onChange={onChange}
        onFocus={() => setIsOnFocusInput(true)}
        placeholder={question.placeholder}
        required={!question.optional}
        value={answer?.value}
      />

      <MnTooltip content={getTooltipContent(disabled, isClosed)}>
        <div className='rd-add' onClick={handleAddRegistry}>
          <MnSvg
            className={classNames('rq-add-register-entry-tooltip', { disabled: !isButtonEnabled })}
            path='/assets/images/pictos/icon/info-light.svg'
            variant={isButtonEnabled ? 'primary' : 'gray500-primary'}
          />
          <div className={classNames('rq-add-register-entry', { disabled: !isButtonEnabled })}>Prendre un numéro</div>
        </div>
      </MnTooltip>

      {contractAnswer && (
        <RegisterAddEntryPopin
          contractId={contract.id}
          defaultAnswer={contractAnswer}
          mode={question.register.type}
          onClose={() => setIsAdding(false)}
          onError={handleError}
          onFinished={handleRegisterFinished}
          opened={isAdding}
        />
      )}

      <MnConfirmationPopin
        cancel='Non'
        clickOutsideToClose={false}
        content={`Le numéro ${answer?.value} est indiqué comme réservé dans le registre. Êtes-vous sûr de vouloir l'utiliser ?`}
        onCancel={handleCancelPopin}
        onValidate={() => setOpenConfirmationPopin(false)}
        opened={openConfirmationPopin}
        validate='Oui'
      />
    </div>
  );
};
