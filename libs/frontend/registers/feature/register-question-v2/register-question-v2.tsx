import React, { ReactElement } from 'react';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterTransactionQuestion } from './register-transaction-question';
import { RegisterManagementQuestion } from './register-management-question';

interface MnRegisterQuestionV2Props extends MnProps {
  answer: Answer;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const MnRegisterQuestionV2 = (props: MnRegisterQuestionV2Props): ReactElement => {
  const { question } = props;

  if (question.register?.type === 'TRANSACTION') {
    return <RegisterTransactionQuestion {...props} />;
  } else if (question.register?.type === 'MANAGEMENT') {
    return <RegisterManagementQuestion {...props} />;
  } else {
    throw new Error('Invalid register type');
  }
};
