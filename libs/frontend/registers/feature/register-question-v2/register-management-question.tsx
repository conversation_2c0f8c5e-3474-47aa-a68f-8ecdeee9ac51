import React, { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@mynotary/frontend/roles/api';
import { selectConnectedUserRole } from '@mynotary/frontend/roles/api';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { useFeatureState } from '@mynotary/frontend/features/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';
import { selectCurrentManagementRegister } from '@mynotary/frontend/registers/store';
import { registerPermissionEntityByType } from '@mynotary/frontend/registers/core';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterQuestionCommon } from './register-question-common';
import { RegisterCommonAddActionDisabled } from './register-question-common-add-action-disabled';
import { some } from 'lodash';
import { selectContract } from '@mynotary/frontend/legals/api';

interface RegisterManagementQuestionProps extends MnProps {
  answer: Answer;
  contractId?: string;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterManagementQuestion = (props: RegisterManagementQuestionProps): ReactElement => {
  const role = useSelector(selectConnectedUserRole);
  const contract = useSelector(selectContract(parseInt(props.contractId ?? '')));
  const managementRegister = useSelector(selectCurrentManagementRegister);
  const { isActive: hasManagementFeature } = useFeatureState(FeatureType.MANAGEMENT_REGISTER_ACCESS);
  const isAllowedContract = some(
    props.question.register.contracts,
    (contractType) => contractType === contract?.legalContractTemplateId
  );
  const isRegisterInitialized = managementRegister?.config != null;

  const canAdd = hasPermission(
    PermissionType.CREATE_ORGANIZATION_REGISTER_ENTRY,
    role,
    registerPermissionEntityByType['MANAGEMENT']
  );

  const canRead = hasPermission(
    PermissionType.READ_ORGANIZATION_REGISTER,
    role,
    registerPermissionEntityByType['MANAGEMENT']
  );

  const canAddManually =
    !isRegisterInitialized ||
    hasPermission(
      PermissionType.CREATE_ORGANIZATION_REGISTER_MANUALLY_ENTRY,
      role,
      registerPermissionEntityByType['MANAGEMENT']
    );

  if (hasManagementFeature && canAdd && isAllowedContract && isRegisterInitialized) {
    return <RegisterQuestionCommon {...props} canAddManually={canAddManually} canRead={canRead} />;
  } else {
    return (
      <RegisterCommonAddActionDisabled
        {...props}
        canAdd={canAdd}
        canAddManually={canAddManually}
        canRead={canRead}
        isAllowedContract={isAllowedContract}
        isRegisterInitialized={isRegisterInitialized}
      />
    );
  }
};
