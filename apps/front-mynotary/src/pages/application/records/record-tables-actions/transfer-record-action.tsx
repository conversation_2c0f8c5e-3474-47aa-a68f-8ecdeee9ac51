import { selectPermission } from '@mynotary/frontend/roles/store';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { selectCurrentMembersOrganizations } from '@mynotary/frontend/user-session/store';
import { ActionIcon } from '@mynotary/frontend/shared/ui';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { RecordOrganizationUpdater } from '@mynotary/frontend/organization-data-transfers/feature';
import { Record } from '@mynotary/frontend/legals/core';
import { defaultTooltipPermission } from 'pages/application/records/record-tables-actions/delete-record-action-table';

interface TransferRecordActionProps {
  onFinishAction: () => void;
  record: Record;
}

export const TransferRecordAction = ({ onFinishAction, record }: TransferRecordActionProps) => {
  const [isUpdatingRecord, setIsUpdatingRecord] = useState(false);

  const hasTransferRecordPermission = useSelector(
    selectPermission(PermissionType.RECORD_TRANSFER, EntityType.DATA_TRANSFERT)
  );

  const currentUserOrganizations = useSelector(selectCurrentMembersOrganizations);

  const canUpdateRecordOrganization = hasTransferRecordPermission && currentUserOrganizations.length > 0;

  const handleFinishAction = () => {
    setIsUpdatingRecord(false);
    onFinishAction();
  };

  return (
    <>
      <ActionIcon
        disabled={!canUpdateRecordOrganization}
        icon={'/assets/images/pictos/icon/arrow-right-circle.svg'}
        label={'Transférer vers une autre organisation'}
        onClick={() => setIsUpdatingRecord(true)}
        testId={'update_record_organization'}
        tooltipOnDisabled={defaultTooltipPermission}
      />
      {isUpdatingRecord && <RecordOrganizationUpdater onCloseAction={handleFinishAction} recordId={record.id} />}
    </>
  );
};
