import { Record } from '@mynotary/frontend/legals/core';
import { selectCurrentMembersOrganizations } from '@mynotary/frontend/user-session/store';
import { ActionIcon } from '@mynotary/frontend/shared/ui';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { RecordCopyToOrganization } from '@mynotary/frontend/organization-data-transfers/feature';

interface DuplicateRecordActionProps {
  onFinishAction: () => void;
  record: Record;
}

export const DuplicateRecordAction = ({ onFinishAction, record }: DuplicateRecordActionProps) => {
  const [isDuplicatingRecord, setIsDuplicatingRecord] = useState(false);

  const currentUserOrganizations = useSelector(selectCurrentMembersOrganizations);

  const canDuplicateRecord = currentUserOrganizations.length > 0;

  const handleFinishAction = () => {
    setIsDuplicatingRecord(false);
    onFinishAction();
  };

  return (
    <>
      <ActionIcon
        disabled={!canDuplicateRecord}
        icon={'/assets/images/pictos/icon/arrow-right-circle.svg'}
        label={'Dupliquer une fiche'}
        onClick={() => setIsDuplicatingRecord(true)}
        testId={'duplicate_record'}
      />
      {isDuplicatingRecord && <RecordCopyToOrganization onCloseAction={handleFinishAction} recordId={record.id} />}
    </>
  );
};
