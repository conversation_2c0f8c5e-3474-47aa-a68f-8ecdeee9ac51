import { ActionIcon } from '@mynotary/frontend/shared/ui';
import { selectIsOperationDefaultRecord } from '@mynotary/frontend/operation-default-records/api';
import { hasPermission } from '@mynotary/frontend/roles/api';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { selectConnectedUserRole } from '@mynotary/frontend/roles/api';
import { Record } from '@mynotary/frontend/legals/core';
import { useSelector } from 'react-redux';

interface DeleteRecordActionTableProps {
  onClick: () => void;
  record: Record;
}

export const DeleteRecordActionTable = ({ onClick, record }: DeleteRecordActionTableProps) => {
  const connectedUser = useSelector(selectCurrentUser);
  const role = useSelector(selectConnectedUserRole);

  const isDefaultRecord = useSelector(selectIsOperationDefaultRecord(record.id));

  const canDeleteRecord =
    (connectedUser?.id === record.creatorUser?.id ||
      hasPermission(PermissionType.DELETE_ORGANIZATION_RECORDS, role, EntityType.ORGANIZATION)) &&
    !isDefaultRecord;

  return (
    <ActionIcon
      disabled={!canDeleteRecord}
      icon={'/assets/images/pictos/icon/trash-light.svg'}
      label={'Supprimer'}
      onClick={onClick}
      testId={'delete'}
      tooltipOnDisabled={
        isDefaultRecord ? 'Vous ne pouvez pas supprimer une fiche par défaut.' : defaultTooltipPermission
      }
    />
  );
};

export const defaultTooltipPermission = `Vous n'avez pas les droits pour effectuer cette action, veuillez contacter votre administrateur.`;
