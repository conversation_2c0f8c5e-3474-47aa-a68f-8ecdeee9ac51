import React, { ReactElement } from 'react';
import { Mn<PERSON>utton, Popover, PopoverActionList, PopoverContent, PopoverTrigger } from '@mynotary/frontend/shared/ui';
import { Record } from '@mynotary/frontend/legals/core';
import { TransferRecordAction } from 'pages/application/records/record-tables-actions/transfer-record-action';
import { DeleteRecordActionTable } from 'pages/application/records/record-tables-actions/delete-record-action-table';
import { DuplicateRecordAction } from 'pages/application/records/record-tables-actions/duplicate-record-action';

interface RecordsTableActionsProps {
  onClick: () => void;
  onFinishAction: () => void;
  record: Record;
}

const RecordsTableActions = ({ onClick, onFinishAction, record }: RecordsTableActionsProps): ReactElement => {
  const [openPopover, setOpenPopover] = React.useState(false);

  return (
    <Popover onOpenChange={setOpenPopover} open={openPopover}>
      <PopoverTrigger asChild={true}>
        <MnButton label='Actions' onClick={() => setOpenPopover(!openPopover)} variant={'secondary'} />
      </PopoverTrigger>
      <PopoverContent>
        <PopoverActionList>
          <DeleteRecordActionTable onClick={onClick} record={record} />
          <TransferRecordAction onFinishAction={onFinishAction} record={record} />
          <DuplicateRecordAction onFinishAction={onFinishAction} record={record} />
        </PopoverActionList>
      </PopoverContent>
    </Popover>
  );
};
export { RecordsTableActions };
